<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 인터넷 권한 -->
    <uses-permission android:name="android.permission.INTERNET"/>

    <!-- 위치 권한 추가 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- 블루투스 권한-->
    <uses-permission android:name="android.permission.BLUETOOTH" />

    <!-- Android 10 이상을 위한 백그라운드 위치 권한 -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <!-- 알림 권한 (Android 13+) -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <!-- 진동 권한-->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <!-- AlarmManager 관련 권한 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.VIBRATE"/>

    <!-- 포그라운드 권한 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    
    <!-- 배터리 최적화 관련 권한 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    
    <!-- WorkManager 관련 권한 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <queries>
        <package android:name="com.android.settings"/>
    </queries>

    <application android:label="대구 버스" android:name="${applicationName}" android:icon="@mipmap/ic_launcher" android:roundIcon="@mipmap/ic_launcher_round" android:networkSecurityConfig="@xml/network_security_config">

        <activity android:name=".MainActivity" android:exported="true" android:launchMode="singleTop" android:theme="@style/LaunchTheme" android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode" android:hardwareAccelerated="true" android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data android:name="io.flutter.embedding.android.NormalTheme" android:resource="@style/NormalTheme" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <!-- 버스 알림 포그라운드 서비스 -->
        <service
            android:name=".services.BusAlertService"
            android:foregroundServiceType="dataSync"
            android:exported="true" />

        <!-- TTS 서비스 -->
        <service android:name=".TTSService" android:foregroundServiceType="dataSync" android:exported="false" />

        <!-- 정류장 추적 서비스 -->
        <service
            android:name=".StationTrackingService"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- AlarmManager를 위한 리시버 등록 -->
        <service android:name="dev.fluttercommunity.plus.androidalarmmanager.AlarmService" android:permission="android.permission.BIND_JOB_SERVICE" android:exported="false"/>
        <receiver android:name="dev.fluttercommunity.plus.androidalarmmanager.AlarmBroadcastReceiver" android:exported="false"/>
        <receiver android:name="dev.fluttercommunity.plus.androidalarmmanager.RebootBroadcastReceiver" android:enabled="false" android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
        <receiver android:name="com.example.daegu_bus_app.NotificationDismissReceiver" android:exported="true">
            <intent-filter>
                <action android:name="com.example.daegu_bus_app.NOTIFICATION_DISMISS" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".AlarmReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.example.daegu_bus_app.AUTO_ALARM" />
            </intent-filter>
        </receiver>

        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data android:name="flutterEmbedding" android:value="2" />
    </application>
</manifest>
